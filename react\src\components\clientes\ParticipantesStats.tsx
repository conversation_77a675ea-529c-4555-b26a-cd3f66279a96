import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { clientesService } from '@/services/clientesService'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Modal } from '@/components/ui/Modal'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface ParticipantesStatsProps {
  empresaId?: number
  onFilterByAtividade?: (atividade: string) => void
  onFilterByDestinacao?: (destinacao: string) => void
  onFilterByAtividadeDestinacao?: (atividade: string, destinacao: string) => void
}

export function ParticipantesStats({
  empresaId,
  onFilterByAtividade,
  onFilterByDestinacao,
  onFilterByAtividadeDestinacao
}: ParticipantesStatsProps) {
  const [modalOpen, setModalOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<'atividade' | 'destinacao' | 'combinacao'>('atividade')

  const { data: estatisticas, isLoading } = useQuery({
    queryKey: ['clientes-estatisticas', empresaId],
    queryFn: () => clientesService.getEstatisticas(empresaId),
    enabled: !!empresaId
  })

  const totalParticipantes = estatisticas?.por_atividade.reduce((sum, item) => sum + item.total, 0) || 0

  const handleFilterClick = (type: 'atividade' | 'destinacao' | 'combinacao', item: any) => {
    if (type === 'atividade' && onFilterByAtividade) {
      onFilterByAtividade(item.atividade)
    } else if (type === 'destinacao' && onFilterByDestinacao) {
      onFilterByDestinacao(item.destinacao)
    } else if (type === 'combinacao' && onFilterByAtividadeDestinacao) {
      onFilterByAtividadeDestinacao(item.atividade, item.destinacao)
    }
    setModalOpen(false)
  }

  if (!empresaId) return null

  return (
    <>
      {/* Card de Resumo - Estilo similar aos Filtros Avançados */}
      <Card gradient>
        <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 p-6 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900/30 rounded-xl flex items-center justify-center">
                <svg className="w-5 h-5 text-primary-600 dark:text-primary-400" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Estatísticas de Participantes
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  Total de {totalParticipantes} participantes cadastrados
                </p>
              </div>
            </div>
            <Button
              variant="primary"
              size="md"
              onClick={() => setModalOpen(true)}
              icon={
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clipRule="evenodd" />
                </svg>
              }
              glow
            >
              Ver Detalhes
            </Button>
          </div>
        </div>
      </Card>

      {/* Modal com Detalhes */}
      <Modal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title="Estatísticas Detalhadas de Participantes"
        size="xl"
      >
        <div className="space-y-6">
          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('atividade')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'atividade'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                Por Atividade
              </button>
              <button
                onClick={() => setActiveTab('destinacao')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'destinacao'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                Por Destinação
              </button>
              <button
                onClick={() => setActiveTab('combinacao')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'combinacao'
                    ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                Combinações
              </button>
            </nav>
          </div>

          {/* Conteúdo das Tabs */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner className="w-6 h-6" />
              </div>
            ) : (
              <>
                {activeTab === 'atividade' && (
                  <div className="space-y-2">
                    {estatisticas?.por_atividade.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        onClick={() => handleFilterClick('atividade', item)}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {item.atividade}
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">
                            {item.total} participantes
                          </span>
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeTab === 'destinacao' && (
                  <div className="space-y-2">
                    {estatisticas?.por_destinacao.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        onClick={() => handleFilterClick('destinacao', item)}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {item.destinacao}
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">
                            {item.total} participantes
                          </span>
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {activeTab === 'combinacao' && (
                  <div className="space-y-2">
                    {estatisticas?.por_atividade_destinacao.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        onClick={() => handleFilterClick('combinacao', item)}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-gray-900 dark:text-white">
                            {item.atividade}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {item.destinacao}
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-semibold text-primary-600 dark:text-primary-400">
                            {item.total} participantes
                          </span>
                          <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>

          {/* Footer com informações */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Clique em qualquer item para filtrar a tabela de participantes por essa categoria.
            </p>
          </div>
        </div>
      </Modal>
    </>
  )
}
